(function(b){b.fn.formset=function(c){var a=b.extend({},b.fn.formset.defaults,c),d=b(this);c=d.parent();var h=function(a,e,f){var c=new RegExp("("+e+"-(\\d+|__prefix__))");e=e+"-"+f;b(a).prop("for")&&b(a).prop("for",b(a).prop("for").replace(c,e));a.id&&(a.id=a.id.replace(c,e));a.name&&(a.name=a.name.replace(c,e))},g=b("#id_"+a.prefix+"-TOTAL_FORMS").prop("autocomplete","off"),k=parseInt(g.val(),10),e=b("#id_"+a.prefix+"-MAX_NUM_FORMS").prop("autocomplete","off"),f=""===e.val()||0<e.val()-g.val();
d.each(function(e){b(this).not("."+a.emptyCssClass).addClass(a.formCssClass)});if(d.length&&f){var m=a.addButton;null===m&&("TR"===d.prop("tagName")?(d=this.eq(-1).children().length,c.append('<tr class="'+a.addCssClass+'"><td colspan="'+d+'"><a href="#">'+a.addText+"</a></tr>"),m=c.find("tr:last a")):(d.filter(":last").after('<div class="'+a.addCssClass+'"><a href="#">'+a.addText+"</a></div>"),m=d.filter(":last").next().find("a")));m.on("click",function(f){f.preventDefault();f=b("#"+a.prefix+"-empty");
var c=f.clone(!0);c.removeClass(a.emptyCssClass).addClass(a.formCssClass).attr("id",a.prefix+"-"+k);c.is("tr")?c.children(":last").append('<div><a class="'+a.deleteCssClass+'" href="#">'+a.deleteText+"</a></div>"):c.is("ul")||c.is("ol")?c.append('<li><a class="'+a.deleteCssClass+'" href="#">'+a.deleteText+"</a></li>"):c.children(":first").append('<span><a class="'+a.deleteCssClass+'" href="#">'+a.deleteText+"</a></span>");c.find("*").each(function(){h(this,a.prefix,g.val())});c.insertBefore(b(f));
b(g).val(parseInt(g.val(),10)+1);k+=1;""!==e.val()&&0>=e.val()-g.val()&&m.parent().hide();c.find("a."+a.deleteCssClass).on("click",function(f){f.preventDefault();c.remove();--k;a.removed&&a.removed(c);b(document).trigger("formset:removed",[c,a.prefix]);f=b("."+a.formCssClass);b("#id_"+a.prefix+"-TOTAL_FORMS").val(f.length);(""===e.val()||0<e.val()-f.length)&&m.parent().show();var d,g=function(){h(this,a.prefix,l)};var l=0;for(d=f.length;l<d;l++)h(b(f).get(l),a.prefix,l),b(f.get(l)).find("*").each(g)});
a.added&&a.added(c);b(document).trigger("formset:added",[c,a.prefix])})}return this};b.fn.formset.defaults={prefix:"form",addText:"add another",deleteText:"remove",addCssClass:"add-row",deleteCssClass:"delete-row",emptyCssClass:"empty-row",formCssClass:"dynamic-form",added:null,removed:null,addButton:null};b.fn.tabularFormset=function(c,a){var d=b(this),h=function(a){b(c).not(".add-row").removeClass("row1 row2").filter(":even").addClass("row1").end().filter(":odd").addClass("row2")},g=function(){"undefined"!==
typeof SelectFilter&&(b(".selectfilter").each(function(a,b){a=b.name.split("-");SelectFilter.init(b.id,a[a.length-1],!1)}),b(".selectfilterstacked").each(function(a,b){a=b.name.split("-");SelectFilter.init(b.id,a[a.length-1],!0)}))},k=function(a){a.find(".prepopulated_field").each(function(){var c=b(this).find("input, select, textarea"),e=c.data("dependency_list")||[],d=[];b.each(e,function(b,c){d.push("#"+a.find(".field-"+c).find("input, select, textarea").attr("id"))});d.length&&c.prepopulate(d,
c.attr("maxlength"))})};d.formset({prefix:a.prefix,addText:a.addText,formCssClass:"dynamic-"+a.prefix,deleteCssClass:"inline-deletelink",deleteText:a.deleteText,emptyCssClass:"empty-form",removed:h,added:function(a){k(a);"undefined"!==typeof DateTimeShortcuts&&(b(".datetimeshortcuts").remove(),DateTimeShortcuts.init());g();h(a)},addButton:a.addButton});return d};b.fn.stackedFormset=function(c,a){var d=b(this),h=function(a){b(c).find(".inline_label").each(function(a){a+=1;b(this).html(b(this).html().replace(/(#\d+)/g,
"#"+a))})},g=function(){"undefined"!==typeof SelectFilter&&(b(".selectfilter").each(function(a,b){a=b.name.split("-");SelectFilter.init(b.id,a[a.length-1],!1)}),b(".selectfilterstacked").each(function(a,b){a=b.name.split("-");SelectFilter.init(b.id,a[a.length-1],!0)}))},k=function(a){a.find(".prepopulated_field").each(function(){var c=b(this).find("input, select, textarea"),d=c.data("dependency_list")||[],e=[];b.each(d,function(b,c){e.push("#"+a.find(".form-row .field-"+c).find("input, select, textarea").attr("id"))});
e.length&&c.prepopulate(e,c.attr("maxlength"))})};d.formset({prefix:a.prefix,addText:a.addText,formCssClass:"dynamic-"+a.prefix,deleteCssClass:"inline-deletelink",deleteText:a.deleteText,emptyCssClass:"empty-form",removed:h,added:function(a){k(a);"undefined"!==typeof DateTimeShortcuts&&(b(".datetimeshortcuts").remove(),DateTimeShortcuts.init());g();h(a)},addButton:a.addButton});return d};b(document).ready(function(){b(".js-inline-admin-formset").each(function(){var c=b(this).data(),a=c.inlineFormset;
switch(c.inlineType){case "stacked":c=a.name+"-group .inline-related";b(c).stackedFormset(c,a.options);break;case "tabular":c=a.name+"-group .tabular.inline-related tbody:first > tr",b(c).tabularFormset(c,a.options)}})})})(django.jQuery);
