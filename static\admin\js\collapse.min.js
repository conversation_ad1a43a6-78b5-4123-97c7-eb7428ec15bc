(function(){var e=function(b,a){return b.nodeName===a.toUpperCase()?b:"BODY"===b.parentNode.nodeName?null:b.parentNode&&e(b.parentNode,a)};window.addEventListener("load",function(){for(var b=document.querySelectorAll("fieldset.collapse"),a=0;a<b.length;a++){var c=b[a];if(0===c.querySelectorAll("div.errors").length){c.classList.add("collapsed");c=c.querySelector("h2");var d=document.createElement("a");d.setAttribute("id","fieldsetcollapser"+a);d.setAttribute("class","collapse-toggle");d.setAttribute("href",
"#");d.textContent=gettext("Show");c.appendChild(document.createTextNode(" ("));c.appendChild(d);c.appendChild(document.createTextNode(")"))}}b=function(a){if(a.target.matches(".collapse-toggle")){a.preventDefault();a.stopPropagation();var b=e(a.target,"fieldset");b.classList.contains("collapsed")?(a.target.textContent=gettext("Hide"),b.classList.remove("collapsed")):(a.target.textContent=gettext("Show"),b.classList.add("collapsed"))}};c=document.querySelectorAll("fieldset.module");for(a=0;a<c.length;a++)c[a].addEventListener("click",
b)})})();
